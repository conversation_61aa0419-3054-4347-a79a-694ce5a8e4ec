<div>
    <form wire:submit.prevent="submit">
        <div class="row gy-4">
            <!-- Field Selection -->
            <div class="col-xl-6">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Field Selection</div>
                    </div>
                    <div class="card-body">
                        <div class="row gy-3">
                            <!-- Field Selection -->
                            <div class="col-xl-12">
                                <label for="field_id" class="form-label">Field <span class="text-danger">*</span></label>
                                <select wire:model.live="field_id" id="field_id" required class="form-select">
                                    <option value="">Select Field</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}">
                                            {{ $field->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Field Information Display -->
                            @if ($showFieldInfo && $selectedField)
                                <div class="col-xl-12">
                                    <div class="alert alert-info" id="fieldInfo">
                                        <h6 class="fw-semibold">{{ $selectedField->name }}</h6>
                                        <p class="mb-1"><strong>Type:</strong> {{ ucfirst($selectedField->type) }}</p>
                                        <p class="mb-1"><strong>Capacity:</strong> {{ $selectedField->capacity }} people</p>
                                        <p class="mb-1"><strong>Working Hours:</strong> {{ $selectedField->opening_time }} - {{ $selectedField->closing_time }}</p>
                                        <p class="mb-1"><strong>Day Rate:</strong> XCG {{ number_format($selectedField->hourly_rate, 2) }}/hr</p>
                                        <p class="mb-1"><strong>Night Rate:</strong> XCG {{ number_format($selectedField->night_hourly_rate, 2) }}/hr (after {{ $selectedField->night_time_start }})</p>
                                        <p class="mb-0"><strong>Booking Duration:</strong> {{ $selectedField->min_booking_hours }}-{{ $selectedField->max_booking_hours }} hours</p>
                                    </div>
                                </div>
                            @endif

                            <!-- Date Selection -->
                            <div class="col-xl-12">
                                <label for="booking_date" class="form-label">Date <span class="text-danger">*</span></label>
                                <input type="date" wire:model.live="booking_date" id="booking_date"
                                       min="{{ date('Y-m-d') }}" required class="form-control">
                            </div>

                            <!-- Duration Selection -->
                            <div class="col-xl-12">
                                <label for="duration_hours" class="form-label">Duration (hours) <span class="text-danger">*</span></label>
                                <select wire:model.live="duration_hours" id="duration_hours" required class="form-select">
                                    @if ($selectedField)
                                        @for ($i = $selectedField->min_booking_hours; $i <= $selectedField->max_booking_hours; $i += 0.5)
                                            <option value="{{ $i }}">{{ $i }} {{ $i == 1 ? 'hour' : 'hours' }}</option>
                                        @endfor
                                    @else
                                        <option value="1">1 hour</option>
                                        <option value="1.5">1.5 hours</option>
                                        <option value="2">2 hours</option>
                                        <option value="2.5">2.5 hours</option>
                                        <option value="3">3 hours</option>
                                        <option value="3.5">3.5 hours</option>
                                        <option value="4">4 hours</option>
                                    @endif
                                </select>
                            </div>

                            <!-- Time Selection -->
                            <div class="col-xl-12">
                                <label for="start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                                <select wire:model.live="start_time" id="start_time" required class="form-select">
                                    <option value="">Select Time</option>
                                    @foreach ($availableTimeSlots as $slot)
                                        <option value="{{ $slot['start_time'] }}">{{ $slot['display'] }}</option>
                                    @endforeach
                                </select>
                                @if (empty($availableTimeSlots) && $field_id && $booking_date && $duration_hours)
                                    <div class="text-muted mt-1">No available time slots for the selected duration.</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="col-xl-6">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Customer Information</div>
                    </div>
                    <div class="card-body">
                        <div class="row gy-3">
                            <div class="col-xl-12">
                                <label for="customer_name" class="form-label">Customer Name</label>
                                <input type="text" wire:model="customer_name" id="customer_name"
                                       class="form-control" placeholder="Enter customer name">
                            </div>
                            <div class="col-xl-12">
                                <label for="customer_email" class="form-label">Email</label>
                                <input type="email" wire:model="customer_email" id="customer_email"
                                       class="form-control" placeholder="Enter email address">
                            </div>
                            <div class="col-xl-12">
                                <label for="customer_phone" class="form-label">Phone</label>
                                <input type="tel" wire:model="customer_phone" id="customer_phone"
                                       class="form-control" placeholder="Enter phone number">
                            </div>
                            <div class="col-xl-12">
                                <label for="special_requests" class="form-label">Special Requests</label>
                                <textarea wire:model="special_requests" id="special_requests"
                                          class="form-control" rows="3" placeholder="Any special requests or notes"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cost Display -->
        @if ($showCostDisplay && $costCalculation)
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-success" id="costDisplay">
                        <h6 class="fw-semibold">Reservation Cost</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Field Cost:</strong> XCG {{ number_format($costCalculation['field_cost'], 2) }}</p>
                                @if (!empty($costCalculation['utility_breakdown']))
                                    <p class="mb-1"><strong>Utilities:</strong></p>
                                    @foreach ($costCalculation['utility_breakdown'] as $utility)
                                        <p class="mb-1 ms-3">• {{ $utility['name'] }}: {{ $utility['hours'] }} hrs × XCG {{ number_format($utility['rate'], 2) }} = XCG {{ number_format($utility['cost'], 2) }}</p>
                                    @endforeach
                                @endif
                            </div>
                            <div class="col-md-6">
                                <p class="mb-0 fs-5"><strong>Total Cost: XCG {{ number_format($costCalculation['total_cost'], 2) }}</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Utilities Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card custom-card shadow-none border">
                    <div class="card-header">
                        <div class="card-title">Additional Utilities (Optional)</div>
                    </div>
                    <div class="card-body {{ $utilitiesEnabled ? 'utility-section-enabled' : 'utility-section-disabled' }}">
                        @if (!$utilitiesEnabled)
                            <div class="alert alert-info">
                                <i class="ri-information-line me-2"></i>
                                Please complete all required fields above to enable utility selection.
                            </div>
                        @endif

                        <div class="row gy-3">
                            <div class="col-md-6">
                                <label for="utility_select" class="form-label">Select Utility</label>
                                <select id="utility_select" class="form-select" {{ !$utilitiesEnabled ? 'disabled' : '' }}>
                                    <option value="">Choose a utility</option>
                                    @foreach ($availableUtilities as $utility)
                                        <option value="{{ $utility->id }}" data-rate="{{ $utility->hourly_rate }}">
                                            {{ $utility->name }} - XCG {{ number_format($utility->hourly_rate, 2) }}/hr
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="utility_quantity" class="form-label">Hours</label>
                                <input type="number" id="utility_quantity" class="form-control"
                                       min="1" value="1" {{ !$utilitiesEnabled ? 'disabled' : '' }}>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-primary w-100"
                                        onclick="addUtilityFromSelect()" {{ !$utilitiesEnabled ? 'disabled' : '' }}>
                                    Add
                                </button>
                            </div>
                        </div>

                        <!-- Selected Utilities -->
                        @if (!empty($utilities))
                            <div class="mt-4">
                                <h6>Selected Utilities:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Utility</th>
                                                <th>Hours</th>
                                                <th>Rate</th>
                                                <th>Cost</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($utilities as $index => $utility)
                                                <tr>
                                                    <td>{{ $utility['name'] }}</td>
                                                    <td>{{ $utility['hours'] }}</td>
                                                    <td>XCG {{ number_format($utility['rate'], 2) }}</td>
                                                    <td>XCG {{ number_format($utility['rate'] * $utility['hours'], 2) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger"
                                                                wire:click="removeUtility({{ $index }})">
                                                            Remove
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <button type="submit" class="btn btn-primary btn-lg">
                    Create Reservation
                </button>
                <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-lg ms-2">
                    Cancel
                </a>
            </div>
        </div>
    </form>

    <script>
        function addUtilityFromSelect() {
            const utilitySelect = document.getElementById('utility_select');
            const quantityInput = document.getElementById('utility_quantity');

            if (utilitySelect.value && quantityInput.value) {
                @this.call('addUtility', utilitySelect.value, parseInt(quantityInput.value));
                utilitySelect.value = '';
                quantityInput.value = 1;
            }
        }
    </script>
</div>

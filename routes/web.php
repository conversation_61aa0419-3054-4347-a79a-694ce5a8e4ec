<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome1');
});

Route::get('/dashboard', function () {
    $user = auth()->user();

    // Check if user exists before accessing methods
    if (! $user) {
        return redirect()->route('login');
    }

    // Redirect to role-specific dashboard
    if ($user->isAdmin()) {
        return redirect()->route('admin.dashboard');
    } elseif ($user->isEmployee()) {
        return redirect()->route('employee.dashboard');
    } elseif ($user->isUser()) {
        return redirect()->route('user.dashboard');
    }

    // Fallback to default dashboard
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin-only routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    Route::get('/users', [App\Http\Controllers\Admin\UserManagementController::class, 'index'])->name('users.index');
    Route::get('/users/create', [App\Http\Controllers\Admin\UserManagementController::class, 'create'])->name('users.create');
    Route::post('/users', [App\Http\Controllers\Admin\UserManagementController::class, 'store'])->name('users.store');
    Route::get('/users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'show'])->name('users.show');
    Route::get('/users/{user}/edit', [App\Http\Controllers\Admin\UserManagementController::class, 'edit'])->name('users.edit');
    Route::put('/users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'update'])->name('users.update');
    Route::delete('/users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'destroy'])->name('users.destroy');
    Route::post('/users/{user}/role', [App\Http\Controllers\Admin\UserManagementController::class, 'updateRole'])->name('users.update-role');
    Route::post('/users/{user}/lock', [App\Http\Controllers\Admin\UserManagementController::class, 'lock'])->name('users.lock');
    Route::post('/users/{user}/unlock', [App\Http\Controllers\Admin\UserManagementController::class, 'unlock'])->name('users.unlock');

    // Field Management Routes (Admin Only)
    Route::resource('fields', App\Http\Controllers\Admin\FieldController::class);

    // Amenity Management Routes (Admin Only)
    Route::resource('amenities', App\Http\Controllers\Admin\AmenityController::class);
    Route::post('amenities/bulk-action', [App\Http\Controllers\Admin\AmenityController::class, 'bulkAction'])->name('amenities.bulk-action');
    Route::get('amenities-debug', function () {
        return view('admin.amenities.debug');
    })->name('amenities.debug');
    Route::get('amenities-test-browser', function () {
        return view('admin.amenities.test-browser');
    })->name('amenities.test-browser');

    // Utility Management Routes (Admin Only)
    Route::resource('utilities', App\Http\Controllers\Admin\UtilityController::class);
    Route::post('utilities/bulk-action', [App\Http\Controllers\Admin\UtilityController::class, 'bulkAction'])->name('utilities.bulk-action');
});

// Employee routes (accessible by employee and admin)
Route::middleware(['auth', 'employee'])->prefix('employee')->name('employee.')->group(function () {
    Route::get('/dashboard', function () {
        return view('employee.dashboard');
    })->name('dashboard');

    Route::get('/features', function () {
        return view('employee.features');
    })->name('features');
});

// User routes (accessible by user, employee, and admin)
Route::middleware(['auth', 'user'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', function () {
        return view('user.dashboard');
    })->name('dashboard');
});

// Profile routes (accessible to all authenticated users)
Route::middleware('auth')->group(function () {
    Route::get('/profile', function () {
        return view('profile.show');
    })->name('profile.show');
    Route::get('/profile/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [App\Http\Controllers\ProfileController::class, 'destroy'])->name('profile.destroy');

    // Calendar and Booking Routes (All authenticated users)
    Route::get('/calendar', [App\Http\Controllers\CalendarController::class, 'index'])->name('calendar.index');
    Route::get('/calendar/events', [App\Http\Controllers\CalendarController::class, 'events'])->name('calendar.events');
    Route::resource('bookings', App\Http\Controllers\BookingController::class);
    Route::post('/bookings/{booking}/confirm', [App\Http\Controllers\BookingController::class, 'confirm'])->name('bookings.confirm');
    Route::post('/bookings/{booking}/cancel', [App\Http\Controllers\BookingController::class, 'cancel'])->name('bookings.cancel');

    Route::post('/calendar/update-reservation/{reservation}', [App\Http\Controllers\CalendarController::class, 'updateReservationDate'])->name('calendar.update-reservation');

    // Reservation Routes
    Route::resource('reservations', App\Http\Controllers\ReservationController::class);
    Route::get('/reservations/{reservation}/details', [App\Http\Controllers\ReservationController::class, 'show'])->name('reservations.details');
    Route::post('/reservations/{reservation}/cancel', [App\Http\Controllers\ReservationController::class, 'cancel'])->name('reservations.cancel');
    Route::post('/reservations/check-availability', [App\Http\Controllers\ReservationController::class, 'checkAvailability'])->name('reservations.check-availability');
    Route::post('/reservations/cost-estimate', [App\Http\Controllers\ReservationController::class, 'getCostEstimate'])->name('reservations.cost-estimate');
});

require __DIR__.'/auth.php';

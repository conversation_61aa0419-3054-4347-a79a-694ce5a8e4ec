<?php

namespace App\Livewire;

use App\Models\Field;
use App\Models\Utility;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use Carbon\Carbon;
use Livewire\Component;

class ReservationForm extends Component
{
    // Form properties
    public $field_id = '';
    public $booking_date = '';
    public $start_time = '';
    public $duration_hours = 1;
    public $customer_name = '';
    public $customer_email = '';
    public $customer_phone = '';
    public $special_requests = '';
    public $utilities = [];

    // Dynamic data
    public $fields = [];
    public $availableTimeSlots = [];
    public $selectedField = null;
    public $costCalculation = null;
    public $availableUtilities = [];

    // UI state
    public $showFieldInfo = false;
    public $showCostDisplay = false;
    public $utilitiesEnabled = false;

    protected $availabilityService;
    protected $costService;

    public function boot(FieldAvailabilityService $availabilityService, ReservationCostService $costService)
    {
        $this->availabilityService = $availabilityService;
        $this->costService = $costService;
    }

    public function mount($selectedField = null, $selectedDate = null, $selectedTime = null)
    {
        $this->fields = Field::active()->orderBy('name')->get();
        $this->availableUtilities = Utility::active()->orderBy('name')->get();

        // Set initial values if provided
        if ($selectedField) {
            $this->field_id = $selectedField->id;
            $this->selectedField = $selectedField;
            $this->showFieldInfo = true;
        }

        if ($selectedDate) {
            $this->booking_date = $selectedDate;
        }

        if ($selectedTime) {
            $this->start_time = $selectedTime;
        }

        // Load initial availability if we have field and date
        if ($this->field_id && $this->booking_date) {
            $this->loadAvailability();
        }
    }

    public function updatedFieldId()
    {
        if ($this->field_id) {
            $this->selectedField = Field::find($this->field_id);
            $this->showFieldInfo = true;
            $this->loadAvailability();
            $this->calculateCost();
            $this->checkUtilityPrerequisites();
        } else {
            $this->selectedField = null;
            $this->showFieldInfo = false;
            $this->showCostDisplay = false;
            $this->availableTimeSlots = [];
            $this->start_time = '';
        }
    }

    public function updatedBookingDate()
    {
        $this->loadAvailability();
        $this->checkUtilityPrerequisites();
    }

    public function updatedDurationHours()
    {
        $this->loadAvailability();
        $this->calculateCost();
    }

    public function updatedStartTime()
    {
        $this->calculateCost();
        $this->checkUtilityPrerequisites();
    }

    public function loadAvailability()
    {
        if (!$this->field_id || !$this->booking_date || !$this->duration_hours) {
            $this->availableTimeSlots = [];
            return;
        }

        $field = Field::find($this->field_id);
        if (!$field) {
            $this->availableTimeSlots = [];
            return;
        }

        $this->availableTimeSlots = $this->availabilityService->getAvailableTimeSlots(
            $field,
            $this->booking_date,
            $this->duration_hours
        );
    }

    public function calculateCost()
    {
        if (!$this->field_id || !$this->start_time || !$this->duration_hours) {
            $this->showCostDisplay = false;
            $this->costCalculation = null;
            return;
        }

        $field = Field::find($this->field_id);
        if (!$field) {
            $this->showCostDisplay = false;
            $this->costCalculation = null;
            return;
        }

        try {
            $this->costCalculation = $this->costService->calculateTotalCostWithUtilities(
                $field,
                $this->duration_hours,
                $this->start_time,
                $this->utilities
            );
            $this->showCostDisplay = true;
        } catch (\Exception $e) {
            $this->showCostDisplay = false;
            $this->costCalculation = null;
        }
    }

    public function checkUtilityPrerequisites()
    {
        $this->utilitiesEnabled = !empty($this->field_id) &&
                                 !empty($this->booking_date) &&
                                 !empty($this->start_time) &&
                                 !empty($this->duration_hours);
    }

    public function addUtility($utilityId, $quantity = 1)
    {
        if (!$this->utilitiesEnabled) {
            return;
        }

        $utility = Utility::find($utilityId);
        if (!$utility) {
            return;
        }

        // Check if utility is already added
        $existingIndex = collect($this->utilities)->search(function ($item) use ($utilityId) {
            return $item['id'] == $utilityId;
        });

        if ($existingIndex !== false) {
            // Update quantity
            $this->utilities[$existingIndex]['hours'] = $quantity;
        } else {
            // Add new utility
            $this->utilities[] = [
                'id' => $utility->id,
                'name' => $utility->name,
                'rate' => $utility->hourly_rate,
                'hours' => $quantity
            ];
        }

        $this->calculateCost();
    }

    public function removeUtility($index)
    {
        unset($this->utilities[$index]);
        $this->utilities = array_values($this->utilities); // Re-index array
        $this->calculateCost();
    }

    public function submit()
    {
        // Validate the form data
        $this->validate([
            'field_id' => 'required|exists:fields,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'duration_hours' => 'required|numeric|min:0.5|regex:/^[0-9]+(\.[05])?$/',
            'customer_name' => 'nullable|string|max:255',
            'customer_email' => 'nullable|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'special_requests' => 'nullable|string|max:1000',
        ]);

        // Prepare the data for submission
        $data = [
            'field_id' => $this->field_id,
            'booking_date' => $this->booking_date,
            'start_time' => $this->start_time,
            'duration_hours' => $this->duration_hours,
            'customer_name' => $this->customer_name,
            'customer_email' => $this->customer_email,
            'customer_phone' => $this->customer_phone,
            'special_requests' => $this->special_requests,
            'utilities' => $this->utilities,
        ];

        // Submit the data directly to the store method
        $request = request();
        $request->merge($data);

        $controller = app(\App\Http\Controllers\ReservationController::class);
        return $controller->store($request);
    }

    public function render()
    {
        return view('livewire.reservation-form');
    }
}
